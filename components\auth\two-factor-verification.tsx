'use client'

import { useState, useTransition } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useTranslations } from 'next-intl'
import { Link } from '../../i18n/navigation'
import { ArrowLeft, Shield, Smartphone, Key, Loader2 } from 'lucide-react'
import { twoFactorLoginAction, type TwoFactorLoginFormData } from '@/lib/actions/auth'
import { toast } from 'sonner'

interface TwoFactorVerificationProps {
  email: string
  password: string
  onBack: () => void
}

export default function TwoFactorVerification({ email, password, onBack }: TwoFactorVerificationProps) {
  const t = useTranslations('Auth')
  const [isPending, startTransition] = useTransition()
  const [twoFactorCode, setTwoFactorCode] = useState('')
  const [useBackupCode, setUseBackupCode] = useState(false)
  const [errors, setErrors] = useState<Record<string, string[]>>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors({})

    if (!twoFactorCode.trim()) {
      toast.error('Please enter your verification code')
      return
    }

    const formData: TwoFactorLoginFormData = {
      email,
      password,
      twoFactorCode: twoFactorCode.trim()
    }

    startTransition(async () => {
      try {
        const result = await twoFactorLoginAction(formData)

        if (result.success) {
          toast.success(result.message)
          // Force session refresh and redirect
          window.location.href = result.redirectTo || '/en'
        } else {
          toast.error(result.message)
          if (result.errors) {
            setErrors(result.errors)
          }
        }
      } catch (error) {
        toast.error('An unexpected error occurred')
        console.error('2FA verification error:', error)
      }
    })
  }

  const handleCodeChange = (value: string) => {
    // Allow only numbers for TOTP codes, or alphanumeric for backup codes
    const sanitized = useBackupCode 
      ? value.replace(/[^a-zA-Z0-9-]/g, '').toUpperCase()
      : value.replace(/\D/g, '')
    
    setTwoFactorCode(sanitized)
    
    // Clear errors when user starts typing
    if (errors.twoFactorCode) {
      setErrors(prev => ({ ...prev, twoFactorCode: [] }))
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <Shield className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle className="text-2xl font-bold">Two-Factor Authentication</CardTitle>
        <CardDescription>
          Enter the verification code from your authenticator app
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Info */}
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600">Signing in as:</p>
          <p className="font-medium text-gray-900">{email}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Code Input */}
          <div className="space-y-2">
            <Label htmlFor="twoFactorCode" className="flex items-center gap-2">
              {useBackupCode ? (
                <>
                  <Key className="h-4 w-4" />
                  Backup Code
                </>
              ) : (
                <>
                  <Smartphone className="h-4 w-4" />
                  Verification Code
                </>
              )}
            </Label>
            <Input
              id="twoFactorCode"
              value={twoFactorCode}
              onChange={(e) => handleCodeChange(e.target.value)}
              placeholder={useBackupCode ? "XXXX-XXXX" : "000000"}
              maxLength={useBackupCode ? 9 : 6}
              className="text-center text-lg font-mono tracking-wider"
              autoComplete="one-time-code"
              autoFocus
            />
            {errors.twoFactorCode && (
              <p className="text-sm text-red-600">{errors.twoFactorCode[0]}</p>
            )}
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full bg-[#09B1BA] hover:bg-[#078A91]"
            disabled={isPending || !twoFactorCode.trim()}
          >
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              'Verify & Sign In'
            )}
          </Button>
        </form>

        <Separator />

        {/* Backup Code Toggle */}
        <div className="text-center space-y-3">
          <button
            type="button"
            onClick={() => {
              setUseBackupCode(!useBackupCode)
              setTwoFactorCode('')
              setErrors({})
            }}
            className="text-sm text-[#09B1BA] hover:text-[#078A91] underline"
          >
            {useBackupCode 
              ? 'Use authenticator app instead' 
              : 'Use backup code instead'
            }
          </button>

          {useBackupCode && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-xs text-yellow-800">
                Backup codes are single-use. Each code can only be used once.
              </p>
            </div>
          )}
        </div>

        <Separator />

        {/* Back Button */}
        <div className="text-center">
          <button
            type="button"
            onClick={onBack}
            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to login
          </button>
        </div>

        {/* Help Text */}
        <div className="text-center space-y-2">
          <p className="text-xs text-gray-500">
            Having trouble? Make sure your device's time is correct.
          </p>
          <Link 
            href="/help/2fa" 
            className="text-xs text-[#09B1BA] hover:text-[#078A91] underline"
          >
            Need help with 2FA?
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
