{"Navigation": {"search": "Search for items", "login": "Log in", "signup": "Sign up", "sell": "Sell your items", "menu": "<PERSON><PERSON>", "account": "Account", "categories": "Categories", "popularItems": "Popular items", "downloadApp": "Download the app", "language": "Language", "articles": "Articles"}, "Categories": {"women": "Women", "men": "Men", "designerItems": "Designer items", "kids": "Kids", "home": "Home", "electronics": "Electronics", "entertainment": "Entertainment", "sports": "Sports", "about": "About", "ourPlatform": "Our platform", "womenClothing": "Women's clothing", "womenShoes": "Women's shoes", "womenBags": "Women's bags", "womenAccessories": "Women's accessories", "menClothing": "Men's clothing", "menShoes": "Men's shoes", "menAccessories": "Men's accessories", "kidsBabies": "Kids & babies", "all": "All", "summerTrends": "Summer trends", "clothing": "Clothing", "shoes": "Shoes", "bags": "Bags", "accessories": "Accessories", "beauty": "Beauty", "exploreByCategory": "Explore by category", "findExactly": "Find exactly what you're looking for in our popular categories", "viewAllCategories": "View all categories"}, "HomePage": {"heroTitle": "Find unique fashion pieces", "heroSubtitle": "Discover thousands of second-hand items from our community", "heroButton": "Start shopping", "heroLink": "Discover how it works", "featuredTitle": "Featured items", "featuredSubtitle": "Discover our community's favorites", "latestTitle": "Latest items added", "latestSubtitle": "Discover the latest from our community", "discoverTitle": "Discover the latest finds", "discoverSubtitle": "Recently added items by our fashion-loving community", "viewAll": "View all", "viewMore": "View more items", "loadMore": "Load more items", "filter": "Filter", "sortBy": "Sort by"}, "ProductCard": {"newWithTags": "New with tags", "veryGoodCondition": "Very good condition", "goodCondition": "Good condition", "satisfactoryCondition": "Satisfactory condition", "size": "Size", "incl": "incl.", "buy": "Buy", "likes": "likes", "views": "views"}, "PromoBanner": {"title": "Ready to start selling?", "subtitle": "Join thousands of sellers and turn your wardrobe into cash", "button": "Start selling now", "features": {"feature1": "List for free", "feature2": "Secure payments", "feature3": "Worldwide shipping"}}, "Testimonials": {"title": "What our community says", "subtitle": "Join thousands of satisfied users", "testimonial1": {"text": "Amazing platform! I sold my entire wardrobe in just a few weeks.", "author": "<PERSON>", "location": "Paris"}, "testimonial2": {"text": "Great quality items and fast shipping. Highly recommended!", "author": "<PERSON>", "location": "Lyon"}, "testimonial3": {"text": "Love the sustainable approach. Found amazing designer pieces!", "author": "<PERSON>", "location": "Marseille"}}, "Auth": {"loginTitle": "Welcome back", "loginSubtitle": "Sign in to your account to continue", "signupTitle": "Create your account", "signupSubtitle": "Join our community of fashion lovers", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm password", "confirmPasswordPlaceholder": "Confirm your password", "firstName": "First name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last name", "lastNamePlaceholder": "Enter your last name", "phone": "Phone number", "phonePlaceholder": "Enter your phone number", "signIn": "Sign in", "signingIn": "Signing in...", "createAccount": "Create account", "forgotPassword": "Forgot password?", "rememberMe": "Remember me", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "continueWithGoogle": "Continue with Google", "continueWithApple": "Continue with Apple", "orContinueWith": "Or continue with", "agreeToTerms": "I agree to the Terms and Conditions", "agreeToMarketing": "I want to receive marketing emails"}, "Footer": {"brand": "<PERSON><PERSON><PERSON>", "description": "The second-hand fashion platform that revolutionizes the way you consume. Buy and sell your favorite clothes with ease.", "aboutUs": "About us", "howItWorks": "How it works", "careers": "Careers", "press": "Press", "sustainability": "Sustainability", "discover": "Discover", "popularBrands": "Popular brands", "help": "Help", "helpCenter": "Help center", "sell": "<PERSON>ll", "buy": "Buy", "trustSafety": "Trust and safety", "contactUs": "Contact us", "legal": "Legal", "terms": "Terms of use", "privacy": "Privacy policy", "cookies": "Cookie policy", "legalNotices": "Legal notices", "disputeResolution": "Dispute resolution", "followUs": "Follow us", "downloadApp": "Download the app", "appStore": "App Store", "googlePlay": "Google Play", "downloadOn": "Download on", "availableOn": "Available on", "copyright": "© 2024 Clothes. All rights reserved.", "france": "France", "support": "<EMAIL>", "women": "Women", "men": "Men", "kids": "Kids", "home": "Home"}, "Common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "close": "Close", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "share": "Share", "favorite": "Favorite", "trending": "Trending", "new": "New", "sale": "Sale", "bestSeller": "Best Seller"}, "SellPage": {"title": "Become a Seller", "subtitle": "Join thousands of sellers and start earning money from your items", "becomeSellerTitle": "Start Your Selling Journey", "becomeSellerSubtitle": "Turn your closet into cash with our easy-to-use platform", "benefit1Title": "<PERSON><PERSON><PERSON>", "benefit1Description": "Set your own prices and keep up to 95% of your sales", "benefit2Title": "Safe & Secure", "benefit2Description": "Protected transactions with buyer protection guarantee", "benefit3Title": "Easy Selling", "benefit3Description": "List items in minutes with our simple upload process", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "email": "Email Address", "emailPlaceholder": "Enter your email address", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "password": "Password", "passwordPlaceholder": "Create a strong password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "continueToStep2": "Continue to Address Info", "addressInfoTitle": "Address Information", "addressInfoSubtitle": "We need your address for shipping and tax purposes", "address": "Street Address", "addressPlaceholder": "Enter your street address", "city": "City", "cityPlaceholder": "Enter your city", "postalCode": "Postal Code", "postalCodePlaceholder": "Enter your postal code", "country": "Country", "selectCountry": "Select your country", "previous": "Previous", "continueToStep3": "Continue to Store Setup", "storeInfoTitle": "Store Information", "storeInfoSubtitle": "Set up your seller profile and store details", "storeName": "Store Name", "storeNamePlaceholder": "Enter your store name", "storeDescription": "Store Description", "storeDescriptionPlaceholder": "Describe your store and what you sell...", "sellerType": "Seller Type", "selectSellerType": "Select your seller type", "individual": "Individual Seller", "business": "Business", "professional": "Professional Seller", "businessNumber": "Business Registration Number", "businessNumberPlaceholder": "Enter your business registration number", "agreeToTerms": "I agree to the", "termsAndConditions": "Terms and Conditions", "agreeToSellerTerms": "I agree to the", "sellerTermsAndConditions": "Seller Terms and Conditions", "agreeToMarketing": "I agree to receive marketing communications and promotional offers", "createSellerAccount": "Create Seller Account", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign In"}}