import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create some sample categories
  const categories = [
    {
      name: 'Women',
      slug: 'women',
      description: 'Women\'s clothing and accessories'
    },
    {
      name: 'Men',
      slug: 'men',
      description: 'Men\'s clothing and accessories'
    },
    {
      name: 'Kids',
      slug: 'kids',
      description: 'Children\'s clothing and accessories'
    },
    {
      name: 'Home',
      slug: 'home',
      description: 'Home decor and furniture'
    }
  ]

  for (const category of categories) {
    await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category
    })
  }

  // Create subcategories
  const womenCategory = await prisma.category.findUnique({
    where: { slug: 'women' }
  })

  const menCategory = await prisma.category.findUnique({
    where: { slug: 'men' }
  })

  if (womenCategory) {
    const womenSubcategories = [
      { name: 'Clothing', slug: 'women-clothing', parentId: womenCategory.id },
      { name: 'Shoes', slug: 'women-shoes', parentId: womenCategory.id },
      { name: 'Bags', slug: 'women-bags', parentId: womenCategory.id },
      { name: 'Accessories', slug: 'women-accessories', parentId: womenCategory.id }
    ]

    for (const subcategory of womenSubcategories) {
      await prisma.category.upsert({
        where: { slug: subcategory.slug },
        update: {},
        create: subcategory
      })
    }
  }

  if (menCategory) {
    const menSubcategories = [
      { name: 'Clothing', slug: 'men-clothing', parentId: menCategory.id },
      { name: 'Shoes', slug: 'men-shoes', parentId: menCategory.id },
      { name: 'Accessories', slug: 'men-accessories', parentId: menCategory.id }
    ]

    for (const subcategory of menSubcategories) {
      await prisma.category.upsert({
        where: { slug: subcategory.slug },
        update: {},
        create: subcategory
      })
    }
  }

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
