'use client'

import { useState, useTransition } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp'
// import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'
import {
  Shield,
  ShieldCheck,
  ShieldX,
  QrCode,
  Copy,
  Download,
  RefreshCw,
  Loader2,
  AlertTriangle,
  CheckCircle,
  Key
} from 'lucide-react'
import {
  setupTwoFactorAction,
  verifyAndEnableTwoFactorAction,
  disableTwoFactorAction,
  regenerateBackupCodesAction
} from '@/lib/actions/2fa'

interface TwoFactorAuthProps {
  user: {
    id: string
    email: string
    twoFactorEnabled?: boolean
  }
}

export default function TwoFactorAuth({ user }: TwoFactorAuthProps) {
  const [isPending, startTransition] = useTransition()
  const [setupData, setSetupData] = useState<{
    qrCodeUrl: string
    backupCodes: string[]
    manualEntryKey: string
  } | null>(null)
  const [verificationCode, setVerificationCode] = useState('')
  const [disablePassword, setDisablePassword] = useState('')
  const [showSetupDialog, setShowSetupDialog] = useState(false)
  const [showDisableDialog, setShowDisableDialog] = useState(false)
  const [showBackupCodes, setShowBackupCodes] = useState(false)
  const [currentStep, setCurrentStep] = useState<'setup' | 'verify' | 'complete'>('setup')

  const handleSetup2FA = () => {
    startTransition(async () => {
      try {
        const result = await setupTwoFactorAction()

        if (result.success && result.data) {
          setSetupData(result.data)
          setCurrentStep('verify')
          setShowSetupDialog(true)
          toast.success(result.message)
        } else {
          toast.error(result.message)
        }
      } catch (error) {
        toast.error('Failed to setup 2FA')
        console.error('2FA setup error:', error)
      }
    })
  }

  const handleVerify2FA = () => {
    if (!verificationCode.trim()) {
      toast.error('Please enter a verification code')
      return
    }

    startTransition(async () => {
      try {
        const result = await verifyAndEnableTwoFactorAction(verificationCode)

        if (result.success) {
          setCurrentStep('complete')
          toast.success(result.message)
          // Close dialog after a delay
          setTimeout(() => {
            setShowSetupDialog(false)
            setCurrentStep('setup')
            setVerificationCode('')
            setSetupData(null)
          }, 2000)
        } else {
          toast.error(result.message)
        }
      } catch (error) {
        toast.error('Failed to verify 2FA')
        console.error('2FA verification error:', error)
      }
    })
  }

  const handleDisable2FA = () => {
    if (!disablePassword.trim()) {
      toast.error('Please enter your password')
      return
    }

    startTransition(async () => {
      try {
        const result = await disableTwoFactorAction(disablePassword)

        if (result.success) {
          setShowDisableDialog(false)
          setDisablePassword('')
          toast.success(result.message)
        } else {
          toast.error(result.message)
        }
      } catch (error) {
        toast.error('Failed to disable 2FA')
        console.error('2FA disable error:', error)
      }
    })
  }

  const handleRegenerateBackupCodes = () => {
    startTransition(async () => {
      try {
        const result = await regenerateBackupCodesAction()

        if (result.success && result.data) {
          setSetupData(prev => prev ? {
            ...prev,
            backupCodes: result.data!.backupCodes
          } : null)
          toast.success(result.message)
        } else {
          toast.error(result.message)
        }
      } catch (error) {
        toast.error('Failed to regenerate backup codes')
        console.error('Backup codes regeneration error:', error)
      }
    })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard')
  }

  const downloadBackupCodes = (codes: string[]) => {
    const content = `Two-Factor Authentication Backup Codes\n\nAccount: ${user.email}\nGenerated: ${new Date().toLocaleString()}\n\n${codes.join('\n')}\n\nKeep these codes safe and secure. Each code can only be used once.`
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `2fa-backup-codes-${user.email}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Backup codes downloaded')
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Two-Factor Authentication
        </CardTitle>
        <CardDescription>
          Secure your account with Google Authenticator or similar TOTP apps
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            {user.twoFactorEnabled ? (
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-100">
                <ShieldCheck className="h-5 w-5 text-green-600" />
              </div>
            ) : (
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-100">
                <ShieldX className="h-5 w-5 text-red-600" />
              </div>
            )}
            <div>
              <h3 className="font-medium">
                {user.twoFactorEnabled ? 'Enabled' : 'Disabled'}
              </h3>
              <p className="text-sm text-gray-500">
                {user.twoFactorEnabled
                  ? 'Your account is protected with 2FA'
                  : 'Add an extra layer of security to your account'
                }
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={user.twoFactorEnabled ? 'default' : 'secondary'}>
              {user.twoFactorEnabled ? 'Active' : 'Inactive'}
            </Badge>
            {user.twoFactorEnabled ? (
              <Dialog open={showDisableDialog} onOpenChange={setShowDisableDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    Disable
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      Disable Two-Factor Authentication
                    </DialogTitle>
                    <DialogDescription>
                      This will remove the extra security layer from your account. Enter your password to confirm.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="disable-password">Password</Label>
                      <Input
                        id="disable-password"
                        type="password"
                        value={disablePassword}
                        onChange={(e) => setDisablePassword(e.target.value)}
                        placeholder="Enter your password"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setShowDisableDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDisable2FA}
                        disabled={isPending}
                      >
                        {isPending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Disabling...
                          </>
                        ) : (
                          'Disable 2FA'
                        )}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            ) : (
              <Button onClick={handleSetup2FA} disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting up...
                  </>
                ) : (
                  'Enable 2FA'
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Backup Codes Management (only if 2FA is enabled) */}
        {user.twoFactorEnabled && (
          <div className="space-y-4">
            <Separator />
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Backup Codes</h3>
                <p className="text-sm text-gray-500">
                  Use these codes if you lose access to your authenticator app
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRegenerateBackupCodes}
                disabled={isPending}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Regenerate
              </Button>
            </div>
          </div>
        )}

        {/* Setup Dialog */}
        <Dialog open={showSetupDialog} onOpenChange={setShowSetupDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {currentStep === 'setup' && 'Setup Two-Factor Authentication'}
                {currentStep === 'verify' && 'Verify Your Setup'}
                {currentStep === 'complete' && 'Setup Complete!'}
              </DialogTitle>
              <DialogDescription>
                {currentStep === 'setup' && 'Scan the QR code with your authenticator app'}
                {currentStep === 'verify' && 'Enter the 6-digit code from your authenticator app'}
                {currentStep === 'complete' && 'Two-factor authentication is now enabled'}
              </DialogDescription>
            </DialogHeader>

            {currentStep === 'verify' && setupData && (
              <div className="space-y-6">
                {/* QR Code */}
                <div className="text-center space-y-4">
                  <div className="flex justify-center">
                    <img
                      src={setupData.qrCodeUrl}
                      alt="2FA QR Code"
                      className="border rounded-lg"
                    />
                  </div>

                  {/* Manual Entry */}
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                      Can't scan? Enter this code manually:
                    </p>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono">
                        {setupData.manualEntryKey}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(setupData.manualEntryKey)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Verification */}
                <div className="space-y-4">
                  <div className="space-y-3">
                    <Label className="text-center block">Verification Code</Label>
                    <div className="flex justify-center">
                      <InputOTP
                        maxLength={6}
                        value={verificationCode}
                        onChange={(value) => setVerificationCode(value)}
                        autoFocus
                      >
                        <InputOTPGroup>
                          <InputOTPSlot index={0} className="w-12 h-12 text-lg" />
                          <InputOTPSlot index={1} className="w-12 h-12 text-lg" />
                          <InputOTPSlot index={2} className="w-12 h-12 text-lg" />
                        </InputOTPGroup>
                        <div className="flex items-center justify-center w-4">
                          <div className="w-2 h-0.5 bg-gray-300 rounded-full"></div>
                        </div>
                        <InputOTPGroup>
                          <InputOTPSlot index={3} className="w-12 h-12 text-lg" />
                          <InputOTPSlot index={4} className="w-12 h-12 text-lg" />
                          <InputOTPSlot index={5} className="w-12 h-12 text-lg" />
                        </InputOTPGroup>
                      </InputOTP>
                    </div>
                    <p className="text-xs text-gray-500 text-center">
                      Enter the 6-digit code from your authenticator app
                    </p>
                  </div>

                  <Button
                    onClick={handleVerify2FA}
                    disabled={isPending || verificationCode.length !== 6}
                    className="w-full bg-[#09B1BA] hover:bg-[#078A91]"
                  >
                    {isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      'Verify & Enable'
                    )}
                  </Button>
                </div>

                {/* Backup Codes */}
                <div className="space-y-4">
                  <Separator />
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Backup Codes</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadBackupCodes(setupData.backupCodes)}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                    <div className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <Key className="h-4 w-4 text-yellow-600 mt-0.5" />
                      <p className="text-sm text-yellow-800">
                        Save these backup codes in a safe place. Each code can only be used once.
                      </p>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                      {setupData.backupCodes.map((code, index) => (
                        <div key={index} className="p-2 bg-gray-100 rounded text-center">
                          {code}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 'complete' && (
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <div className="flex items-center justify-center w-16 h-16 rounded-full bg-green-100">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <p className="text-green-600 font-medium">
                  Two-factor authentication is now active!
                </p>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
