// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String
  lastName  String
  phone     String?
  password  String
  role      UserRole @default(BUYER)
  
  // Profile information
  avatar    String?
  isVerified Boolean @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  addresses Address[]
  seller    Seller?
  orders    Order[]
  reviews   Review[]
  favorites Favorite[]
  
  @@map("users")
}

model Address {
  id         String  @id @default(cuid())
  userId     String
  street     String
  city       String
  postalCode String
  country    String
  isDefault  Boolean @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]
  
  @@map("addresses")
}

model Seller {
  id              String     @id @default(cuid())
  userId          String     @unique
  storeName       String
  storeDescription String?
  sellerType      SellerType
  businessNumber  String?
  
  // Seller stats
  rating          Float?
  totalSales      Int        @default(0)
  isVerified      Boolean    @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  products Product[]
  
  @@map("sellers")
}

model Category {
  id          String @id @default(cuid())
  name        String @unique
  slug        String @unique
  description String?
  parentId    String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]
  
  @@map("categories")
}

model Product {
  id          String        @id @default(cuid())
  sellerId    String
  categoryId  String
  title       String
  description String
  price       Float
  condition   ProductCondition
  size        String?
  brand       String?
  color       String?
  material    String?
  
  // Product status
  status      ProductStatus @default(ACTIVE)
  isPromoted  Boolean       @default(false)
  views       Int           @default(0)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  seller    Seller       @relation(fields: [sellerId], references: [id], onDelete: Cascade)
  category  Category     @relation(fields: [categoryId], references: [id])
  images    ProductImage[]
  orderItems OrderItem[]
  reviews   Review[]
  favorites Favorite[]
  
  @@map("products")
}

model ProductImage {
  id        String @id @default(cuid())
  productId String
  url       String
  altText   String?
  order     Int    @default(0)
  
  // Timestamps
  createdAt DateTime @default(now())
  
  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("product_images")
}

model Order {
  id        String      @id @default(cuid())
  userId    String
  addressId String
  status    OrderStatus @default(PENDING)
  
  // Order totals
  subtotal     Float
  shippingCost Float
  tax          Float
  total        Float
  
  // Payment information
  paymentMethod String?
  paymentStatus PaymentStatus @default(PENDING)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user      User        @relation(fields: [userId], references: [id])
  address   Address     @relation(fields: [addressId], references: [id])
  items     OrderItem[]
  
  @@map("orders")
}

model OrderItem {
  id        String @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float
  
  // Timestamps
  createdAt DateTime @default(now())
  
  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
  
  @@map("order_items")
}

model Review {
  id        String @id @default(cuid())
  userId    String
  productId String
  rating    Int
  comment   String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId])
  @@map("reviews")
}

model Favorite {
  id        String @id @default(cuid())
  userId    String
  productId String
  
  // Timestamps
  createdAt DateTime @default(now())
  
  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId])
  @@map("favorites")
}

// Enums
enum UserRole {
  BUYER
  SELLER
  ADMIN
}

enum SellerType {
  INDIVIDUAL
  BUSINESS
  PROFESSIONAL
}

enum ProductCondition {
  NEW
  LIKE_NEW
  GOOD
  FAIR
  POOR
}

enum ProductStatus {
  ACTIVE
  SOLD
  INACTIVE
  PENDING_APPROVAL
}

enum OrderStatus {
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}
