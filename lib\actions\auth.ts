'use server'

import { signIn, signOut, auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { z } from 'zod'
import { redirect } from 'next/navigation'
import { AuthError } from 'next-auth'

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

const signupSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

const sellerRegistrationSchema = z.object({
  // Personal Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),

  // Address Information
  address: z.string().min(5, 'Address must be at least 5 characters'),
  city: z.string().min(2, 'City must be at least 2 characters'),
  postalCode: z.string().min(3, 'Postal code must be at least 3 characters'),
  country: z.string().min(2, 'Country is required'),

  // Seller Information
  storeName: z.string().min(2, 'Store name must be at least 2 characters'),
  storeDescription: z.string().optional(),
  sellerType: z.enum(['INDIVIDUAL', 'BUSINESS', 'PROFESSIONAL']),
  businessNumber: z.string().optional(),

  // Terms
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms'),
  agreeToSellerTerms: z.boolean().refine(val => val === true, 'You must agree to the seller terms'),
  agreeToMarketing: z.boolean().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// Type definitions
export type LoginFormData = z.infer<typeof loginSchema>
export type SignupFormData = z.infer<typeof signupSchema>
export type SellerRegistrationFormData = z.infer<typeof sellerRegistrationSchema>

export interface ActionResult {
  success: boolean
  message: string
  errors?: Record<string, string[]>
  redirectTo?: string
}

export async function loginAction(formData: LoginFormData): Promise<ActionResult> {
  try {
    // Validate input
    const validatedData = loginSchema.parse(formData)

    // Use Auth.js signIn
    const result = await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    })

    if (result?.error) {
      return {
        success: false,
        message: 'Invalid email or password'
      }
    }

    // Get user to determine redirect
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      include: { seller: true }
    })

    return {
      success: true,
      message: 'Login successful',
      redirectTo: user?.seller ? '/dashboard' : '/'
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: 'Validation failed',
        errors: error.flatten().fieldErrors
      }
    }

    if (error instanceof AuthError) {
      return {
        success: false,
        message: 'Invalid email or password'
      }
    }

    console.error('Login error:', error)
    return {
      success: false,
      message: 'An error occurred during login'
    }
  }
}

export async function signupAction(formData: SignupFormData): Promise<ActionResult> {
  try {
    // Validate input
    const validatedData = signupSchema.parse(formData)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return {
        success: false,
        message: 'User with this email already exists'
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user
    const user = await prisma.user.create({
      data: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        name: `${validatedData.firstName} ${validatedData.lastName}`,
        email: validatedData.email,
        phone: validatedData.phone,
        password: hashedPassword,
        role: 'BUYER'
      }
    })

    // Sign in the user
    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    })

    return {
      success: true,
      message: 'Account created successfully',
      redirectTo: '/'
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: 'Validation failed',
        errors: error.flatten().fieldErrors
      }
    }

    console.error('Signup error:', error)
    return {
      success: false,
      message: 'An error occurred during signup'
    }
  }
}

export async function sellerRegistrationAction(formData: SellerRegistrationFormData): Promise<ActionResult> {
  try {
    // Validate input
    const validatedData = sellerRegistrationSchema.parse(formData)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return {
        success: false,
        message: 'User with this email already exists'
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user and seller in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          name: `${validatedData.firstName} ${validatedData.lastName}`,
          email: validatedData.email,
          phone: validatedData.phone,
          password: hashedPassword,
          role: 'SELLER'
        }
      })

      // Create address
      await tx.address.create({
        data: {
          userId: user.id,
          street: validatedData.address,
          city: validatedData.city,
          postalCode: validatedData.postalCode,
          country: validatedData.country,
          isDefault: true
        }
      })

      // Create seller profile
      await tx.seller.create({
        data: {
          userId: user.id,
          storeName: validatedData.storeName,
          storeDescription: validatedData.storeDescription,
          sellerType: validatedData.sellerType,
          businessNumber: validatedData.businessNumber
        }
      })

      return user
    })

    // Sign in the user
    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    })

    return {
      success: true,
      message: 'Seller account created successfully',
      redirectTo: '/dashboard'
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: 'Validation failed',
        errors: error.flatten().fieldErrors
      }
    }

    console.error('Seller registration error:', error)
    return {
      success: false,
      message: 'An error occurred during seller registration'
    }
  }
}

export async function logoutAction(): Promise<ActionResult> {
  try {
    await signOut({ redirect: false })

    return {
      success: true,
      message: 'Logged out successfully',
      redirectTo: '/'
    }
  } catch (error) {
    console.error('Logout error:', error)
    return {
      success: false,
      message: 'An error occurred during logout'
    }
  }
}

export async function getCurrentUser() {
  try {
    const session = await auth()

    if (!session?.user?.email) return null

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        seller: true,
        addresses: true
      }
    })

    return user
  } catch (error) {
    console.error('Get current user error:', error)
    return null
  }
}

// Google OAuth action
export async function googleSignInAction() {
  try {
    await signIn('google', { redirectTo: '/' })
  } catch (error) {
    console.error('Google sign in error:', error)
    throw error
  }
}
