'use server'

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

// Simple validation functions
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function validatePassword(password: string): boolean {
  return password.length >= 6
}

// Type definitions
export interface LoginFormData {
  email: string
  password: string
}

export interface SignupFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  confirmPassword: string
}

export interface SellerRegistrationFormData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  password: string
  confirmPassword: string

  // Address Information
  address: string
  city: string
  postalCode: string
  country: string

  // Seller Information
  storeName: string
  storeDescription?: string
  sellerType: 'INDIVIDUAL' | 'BUSINESS' | 'PROFESSIONAL'
  businessNumber?: string

  // Terms
  agreeToTerms: boolean
  agreeToSellerTerms: boolean
  agreeToMarketing?: boolean
}

export interface ActionResult {
  success: boolean
  message: string
  errors?: Record<string, string[]>
  redirectTo?: string
}

// Mock user data for demonstration
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'John',
    lastName: 'Doe',
    role: 'BUYER'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'SELLER',
    seller: {
      storeName: 'Jane\'s Store',
      sellerType: 'INDIVIDUAL'
    }
  }
]

export async function loginAction(formData: LoginFormData): Promise<ActionResult> {
  try {
    // Basic validation
    if (!validateEmail(formData.email)) {
      return {
        success: false,
        message: 'Invalid email address',
        errors: { email: ['Invalid email address'] }
      }
    }

    if (!validatePassword(formData.password)) {
      return {
        success: false,
        message: 'Password must be at least 6 characters',
        errors: { password: ['Password must be at least 6 characters'] }
      }
    }

    // Find user (mock implementation)
    const user = mockUsers.find(u => u.email === formData.email)

    if (!user || user.password !== formData.password) {
      return {
        success: false,
        message: 'Invalid email or password'
      }
    }

    // Set simple auth cookie
    const cookieStore = await cookies()
    cookieStore.set('auth-user', JSON.stringify({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })

    return {
      success: true,
      message: 'Login successful',
      redirectTo: user.role === 'SELLER' ? '/dashboard' : '/'
    }

  } catch (error) {
    console.error('Login error:', error)
    return {
      success: false,
      message: 'An error occurred during login'
    }
  }
}

export async function signupAction(formData: SignupFormData): Promise<ActionResult> {
  try {
    // Basic validation
    if (!formData.firstName || formData.firstName.length < 2) {
      return {
        success: false,
        message: 'First name must be at least 2 characters',
        errors: { firstName: ['First name must be at least 2 characters'] }
      }
    }

    if (!formData.lastName || formData.lastName.length < 2) {
      return {
        success: false,
        message: 'Last name must be at least 2 characters',
        errors: { lastName: ['Last name must be at least 2 characters'] }
      }
    }

    if (!validateEmail(formData.email)) {
      return {
        success: false,
        message: 'Invalid email address',
        errors: { email: ['Invalid email address'] }
      }
    }

    if (!validatePassword(formData.password)) {
      return {
        success: false,
        message: 'Password must be at least 6 characters',
        errors: { password: ['Password must be at least 6 characters'] }
      }
    }

    if (formData.password !== formData.confirmPassword) {
      return {
        success: false,
        message: 'Passwords do not match',
        errors: { confirmPassword: ['Passwords do not match'] }
      }
    }

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === formData.email)
    if (existingUser) {
      return {
        success: false,
        message: 'User with this email already exists'
      }
    }

    // Create new user (mock implementation)
    const newUser = {
      id: String(mockUsers.length + 1),
      email: formData.email,
      password: formData.password,
      firstName: formData.firstName,
      lastName: formData.lastName,
      role: 'BUYER' as const
    }

    mockUsers.push(newUser)

    // Set auth cookie
    const cookieStore = await cookies()
    cookieStore.set('auth-user', JSON.stringify({
      id: newUser.id,
      email: newUser.email,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      role: newUser.role
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })

    return {
      success: true,
      message: 'Account created successfully',
      redirectTo: '/'
    }

  } catch (error) {
    console.error('Signup error:', error)
    return {
      success: false,
      message: 'An error occurred during signup'
    }
  }
}

export async function sellerRegistrationAction(formData: SellerRegistrationFormData): Promise<ActionResult> {
  try {
    // Basic validation
    if (!formData.firstName || formData.firstName.length < 2) {
      return {
        success: false,
        message: 'First name must be at least 2 characters',
        errors: { firstName: ['First name must be at least 2 characters'] }
      }
    }

    if (!validateEmail(formData.email)) {
      return {
        success: false,
        message: 'Invalid email address',
        errors: { email: ['Invalid email address'] }
      }
    }

    if (!validatePassword(formData.password)) {
      return {
        success: false,
        message: 'Password must be at least 6 characters',
        errors: { password: ['Password must be at least 6 characters'] }
      }
    }

    if (formData.password !== formData.confirmPassword) {
      return {
        success: false,
        message: 'Passwords do not match',
        errors: { confirmPassword: ['Passwords do not match'] }
      }
    }

    if (!formData.agreeToTerms) {
      return {
        success: false,
        message: 'You must agree to the terms and conditions',
        errors: { agreeToTerms: ['You must agree to the terms and conditions'] }
      }
    }

    if (!formData.agreeToSellerTerms) {
      return {
        success: false,
        message: 'You must agree to the seller terms and conditions',
        errors: { agreeToSellerTerms: ['You must agree to the seller terms and conditions'] }
      }
    }

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === formData.email)
    if (existingUser) {
      return {
        success: false,
        message: 'User with this email already exists'
      }
    }

    // Create new seller (mock implementation)
    const newSeller = {
      id: String(mockUsers.length + 1),
      email: formData.email,
      password: formData.password,
      firstName: formData.firstName,
      lastName: formData.lastName,
      role: 'SELLER' as const,
      seller: {
        storeName: formData.storeName,
        storeDescription: formData.storeDescription,
        sellerType: formData.sellerType,
        businessNumber: formData.businessNumber,
        isVerified: false
      },
      address: {
        street: formData.address,
        city: formData.city,
        postalCode: formData.postalCode,
        country: formData.country
      }
    }

    mockUsers.push(newSeller)

    // Set auth cookie
    const cookieStore = await cookies()
    cookieStore.set('auth-user', JSON.stringify({
      id: newSeller.id,
      email: newSeller.email,
      firstName: newSeller.firstName,
      lastName: newSeller.lastName,
      role: newSeller.role,
      seller: newSeller.seller
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })

    return {
      success: true,
      message: 'Seller account created successfully',
      redirectTo: '/dashboard'
    }

  } catch (error) {
    console.error('Seller registration error:', error)
    return {
      success: false,
      message: 'An error occurred during seller registration'
    }
  }
}

export async function logoutAction(): Promise<ActionResult> {
  try {
    const cookieStore = await cookies()
    cookieStore.delete('auth-user')

    return {
      success: true,
      message: 'Logged out successfully',
      redirectTo: '/'
    }
  } catch (error) {
    console.error('Logout error:', error)
    return {
      success: false,
      message: 'An error occurred during logout'
    }
  }
}

export async function getCurrentUser() {
  try {
    const cookieStore = await cookies()
    const userCookie = cookieStore.get('auth-user')?.value

    if (!userCookie) return null

    const user = JSON.parse(userCookie)

    // Find full user data from mock users
    const fullUser = mockUsers.find(u => u.id === user.id)

    return fullUser || null
  } catch (error) {
    console.error('Get current user error:', error)
    return null
  }
}
